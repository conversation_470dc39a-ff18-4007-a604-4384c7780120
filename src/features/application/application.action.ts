'use server'

import {ActionResult, ApplicationType} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {getAppSource, handleActionErrorResponse} from "@/utils/helpers-server";
import applicationRepository from "@/features/application/application.repository";
import {NotFoundError} from "@/lib/error";

export async function getApplicationByHostname(): Promise<ActionResult<ApplicationType>> {
    try {
        await connectToDatabase()

        const source = await getAppSource()

        let applicationFromDb = await applicationRepository.getByHost(source)

        if (!applicationFromDb) {
            applicationFromDb = await applicationRepository.seed(source);
        }

        if (!applicationFromDb) {
            throw new NotFoundError()
        }

        return {
            success: true,
            data: {
                applicationId: applicationFromDb._id.toString(),
                source: applicationFromDb.source,
                status: applicationFromDb.status,
                pattern_login: applicationFromDb?.pattern_login,
                title: applicationFromDb.title,
                ga_containerId: applicationFromDb.ga_containerId
            }
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getApplications(): Promise<ActionResult<ApplicationType[]>> {
    try {
        await connectToDatabase()

        const applicationsFromDb = await applicationRepository.getAll()

        return {
            success: true,
            data: applicationsFromDb.map((item) => {
                return {
                    applicationId: item._id.toString(),
                    title: item.title,
                    source: item.source,
                    pattern_login: item.pattern_login,
                    status: item.status,
                    ga_containerId: item.ga_containerId,
                    ga_measurementId: item.ga_measurementId,
                    ga_apiSecret: item.ga_apiSecret,
                }
            })
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function updateApplication(data: {
    id: string;
    title: string,
    pattern_login: string
    ga_containerId?: string
    ga_measurementId?: string
    ga_apiSecret?: string
}): Promise<ActionResult> {
    try {
        await connectToDatabase()

        await applicationRepository.findOneAndUpdate(data)

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}